{"name": "kaku", "scripts": {"dev": "npm run build-client && vite dev", "build": "npm run build-client && vite build", "deploy": "npm run build && npm run tailwind:build && wrangler deploy --minify", "tailwind:build": "tailwindcss -i src/main.css -o public/css/styles.css", "tailwind:watch": "tailwindcss -i src/main.css -o public/css/styles.css --watch", "cf:typegen": "wrangler types --env-interface Env", "test": "npm run test:node && npm run test:workers", "test:node": "vitest run --config vitest.unit.config.ts", "test:workers": "vitest run --config vitest.integration.config.ts", "test-form-generation": "node --import tsx test/scripts/measureLLMCallDuration.ts testOpenAIFormGeneration", "test-parallel-llm-calls-performance": "node --import tsx test/scripts/measureLLMCallDuration.ts measureParallelCallsPerformance", "test-llm-prompt-rules": "node --import tsx test/scripts/llmPromptRules.ts", "test-llm-prompt-rules-test": "vitest run test/integration/llm-prompt-rules.test.ts", "test:gemini:performance": "node --import tsx test/scripts/geminiPerformance.ts performance 5", "test:gemini:single": "node --import tsx test/scripts/geminiPerformance.ts single", "test:gemini:htmx": "node --import tsx test/scripts/htmxFormGeneratorTest.ts", "test:list-durable-objects": "node --import tsx test/scripts/listDurableObjects.ts", "test:watch": "vitest", "pretty": "prettier --write \"./**/*.{js,jsx,mjs,cjs,ts,tsx,json}\"", "build-client": "node scripts/build-client.mjs", "watch": "node scripts/watch-client.mjs"}, "dependencies": {"@anthropic-ai/sdk": "^0.54.0", "@google/genai": "^1.9.0", "@hyperbrowser/sdk": "^0.44.0", "@twind/core": "^1.1.3", "@twind/preset-tailwind": "^1.1.4", "agents": "^0.0.54", "argon2-wasm-edge": "^1.0.23", "hono": "^4.7.5", "hono-agents": "^0.0.44", "openai": "^4.87.1", "pixelmatch": "^7.1.0", "tsx": "^4.19.4", "upng-js": "^2.1.0"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.7.1", "@cloudflare/vitest-pool-workers": "^0.8.2", "@tailwindcss/vite": "^4.1.10", "@types/jsdom": "^21.1.7", "@types/upng-js": "^2.1.5", "chokidar-cli": "^3.0.0", "cloudflare": "^4.5.0", "devtools-protocol": "^0.0.1473885", "esbuild": "^0.19.0", "gpt-tokenizer": "^2.1.2", "jsdom": "^26.1.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^3.4.17", "terser": "^5.30.4", "tslib": "^2.8.1", "vite": "^6.1.0", "vitest": "3.0.9", "wrangler": "^4.26.0"}}