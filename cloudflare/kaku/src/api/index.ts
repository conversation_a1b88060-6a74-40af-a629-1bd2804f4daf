import { Hono } from 'hono';
import { agentsMiddleware } from 'hono-agents';
import { <PERSON>kuApp } from '../common/types';
import { DashboardLayoutCard, LayoutWithCard } from '../ui/components/layout';
import { passwordEncoder } from '../common/security/passwordEncoder';
import { capitalize } from '../workflow/utils/helpers';
import { BrowserStateService } from '../workflow/BrowserStateService';
import { R2BrowserStateRepository } from '../workflow/R2BrowserStateRepository';
import { SimulatedLoginHandler } from './simulated-login-handler';
import { CDPBrowserDataAdapter } from '../workflow/adapters/CDPBrowserDataAdapter';
import { BrowserServiceFactory } from '../workflow/services';
import { CDP } from '../browser/simple-cdp';
import { platformDetails, PlatformTypes } from '../ui/constants';
import { userAuthMiddleware } from './user-auth-middleware';
import { linkValidationMiddleware } from './middleware/link-validation';

const app = new Hono<KakuApp>();

// Note: Agent class names are transformed to kebab-case in URLs
// Example: ConnectionAgent → /agents/connection-agent/[platformId]
// In our case, it's /agents/connections/[platformId]
app
  .use(
    '*',
    agentsMiddleware({
      options: {
        cors: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type', // 'Access-Control-Allow-Headers': 'Content-Type', //
        },
        prefix: 'agents',
        // onBeforeRequest: async (request) => {
        //   const response = await validateAuth(request);
        //   if (response instanceof Response) {
        //     return response;
        //   }
        // },
      },
      onError: (error) => {
        console.error('Agent middleware error:', error);
      },
    }),
  )
  .get('/:linkId/:serviceId', linkValidationMiddleware, async (c) => {
    const userId = c.get('userId');
    const linkId = c.get('linkId');
    const serviceId = c.get('serviceId');

    const connectionDOName = `${linkId}`;
    const agent = c.env.Connections.idFromName(connectionDOName);
    const stub = c.env.Connections.get(agent);
    await stub.setName(connectionDOName);
    c.executionCtx.waitUntil(
      stub.eagerlyInitializeResources(serviceId as PlatformTypes, userId, linkId),
    );

    return c.html(DashboardLayoutCard(userId, linkId, serviceId, capitalize(serviceId)));
  })
  .get('/:linkId/:serviceId/flow', linkValidationMiddleware, (c) => {
    const userId = c.get('userId');
    const serviceId = c.get('serviceId');
    const linkId = c.get('linkId');

    const url = `${c.env.KAKU_WS_ENDPOINT}/agents/connections/${linkId}`;
    return c.html(
      LayoutWithCard(
        { wsEndpoint: url, linkId },
        {
          serviceId: serviceId,
          serviceName: capitalize(serviceId),
          serviceLogo: `/fb.png`,
          serviceTitle: 'Testing Forms',
          formTitle: 'Testing Forms',
          serviceDescription: 'Testing Forms',
          liveViewToggleText: 'Show Live View',
          loadingText: 'Processing...',
        },
      ),
    );
  })
  .get('/:linkId/:serviceId/reset', linkValidationMiddleware, async (c) => {
    const userId = c.get('userId');
    const serviceId = c.get('serviceId');
    const linkId = c.get('linkId');
    const coordinatorStub = c.get('coordinatorStub');

    try {
      console.log(`[API] Resetting link ${linkId} for platform ${serviceId}`);

      const resetResult = await coordinatorStub.resetLink(linkId, userId);

      if (!resetResult) {
        return c.html(`<h1>Link not found or not active</h1>`, 404);
      }

      console.log(`[API] Link reset successful, new agent ID: ${resetResult.newAgentId}`);

      const { linkInfo } = resetResult;

      // Redirect to the updated link URL
      return c.redirect(linkInfo.url);
    } catch (error) {
      console.error(`[API] Error resetting link ${linkId}:`, error);

      // Dummy error page for now
      if (error instanceof Error && error.message.includes('Retry limit exceeded')) {
        return c.html(
          `
          <div class="h-screen w-full flex flex-col items-center justify-center bg-white text-black">
            <div class="flex flex-col items-center p-6 max-w-md w-full">
              <div class="flex justify-center mb-6">
                <div class="w-32 h-32 bg-orange-100 rounded-full flex items-center justify-center border-2 border-orange-300">
                  <div class="text-orange-600 font-bold" style="font-size: 3rem; line-height: 1;">⚠</div>
                </div>
              </div>
              <h2 class="text-base font-bold text-center mb-3" style="color: #49454E;">Maximum Retry Attempts Reached</h2>
              <div class="text-base text-gray-600 text-center mb-6 px-6">
                You have reached the maximum number of retry attempts (3) for this platform.
                Please try again later or contact our support team if the issue persists.
              </div>
              <div class="w-full px-6 mb-4">
                <button
                  class="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-700 w-full"
                  onclick="window.history.back()"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        `,
          429,
        );
      }

      return c.html(
        `<h1>Error resetting link: ${error instanceof Error ? error.message : 'Unknown error'}</h1>`,
        500,
      );
    }
  })
  .post('/create-link', async (c) => {
    const body = await c.req.json();
    const serviceId = body.serviceId as PlatformTypes;
    const userId = body.userId as string; // TODO: this will be removed once we have proper authentication

    const platformDetails2 = platformDetails[serviceId];
    if (!platformDetails2) {
      return c.json({ error: 'Invalid serviceId' }, 400);
    }

    const coordinator = c.env.CoordinatorDO.idFromName(userId);
    const stub = c.env.CoordinatorDO.get(coordinator);
    const linkResponse = await stub.createLink(serviceId, userId);
    return c.json(linkResponse);
  })

  .get('/test-password', async (c) => {
    const matches = await passwordEncoder().matches(
      'this is an awesome password',
      '{argon2@SpringSecurity_v5_8}$argon2id$v=19$m=16384,t=2,p=1$J1lDBqWTpije3hfNicl6nA$Ktm+S5MLIWMeGMJ3v7QWCCW6U83Ub4MUisYPwlG8Zcs',
    );
    return c.html(`Password matches: ${matches}`);
  })
  // DEPRECATED: Legacy endpoint using old userId:platformId naming scheme
  // New agents use linkId-based naming. This endpoint is kept for backward compatibility only.
  .get('/handle/:userId/:platformId', async (c) => {
    const userId = c.req.param().userId;
    const platformId = c.req.param().platformId;
    const connectionDOName = `${userId}:${platformId}`;
    const agent = c.env.Connections.idFromName(connectionDOName);
    const stub = c.env.Connections.get(agent);
    await stub.setName(connectionDOName);
    await stub.handleFlowInitiate({ platform: 'test' });
    return c.json({ success: true });
  })
  .post('/demo-session/:userId/:platformId', async (c) => {
    try {
      const userId = c.req.param().userId;
      const platformId = c.req.param().platformId;
      const browserStateService = new BrowserStateService(
        new R2BrowserStateRepository(c.env.SCREENSHOTS_INBOUND_BUCKET),
      );

      const storedSession = await browserStateService.getBrowserState(userId, platformId);

      if (!storedSession) {
        return c.json({
          success: false,
          error: 'No stored session found for this user and platform',
        });
      }

      console.log(`✓ Found stored session with ${storedSession.cookies.length} cookies`);

      const browserService = BrowserServiceFactory.createFromEnvironment(c.env);
      const browserSession = await browserService.createSession({
        browserArgs: [],
      });

      console.log('✓ Created new browser session');

      const cdpClient = new CDP({ webSocketDebuggerUrl: browserSession.wsEndpoint });
      const targetInfo = await cdpClient.Target.createTarget({ url: 'about:blank' });
      console.log('✓ Created new target:', targetInfo.targetId);

      const { sessionId } = await cdpClient.Target.attachToTarget({
        targetId: targetInfo.targetId,
        flatten: true,
      });

      await cdpClient.Page.enable(undefined, sessionId);
      await cdpClient.Runtime.enable(undefined, sessionId);

      console.log('✓ CDP session established with sessionId:', sessionId);

      const browserDataAdapter = new CDPBrowserDataAdapter(cdpClient, sessionId);
      await browserStateService.loadBrowserStateToPage(browserDataAdapter, userId, platformId);

      const targetUrl =
        platformDetails[platformId as keyof typeof platformDetails]?.loginLink ||
        'https://example.com';

      await cdpClient.Page.navigate({ url: targetUrl }, sessionId);

      await new Promise((resolve) => {
        const handler = ({ params }: { params: any }) => {
          if (params.name === 'networkIdle') {
            cdpClient.Page.removeEventListener('loadEventFired', handler);
            resolve(undefined);
          }
        };
        cdpClient.Page.addEventListener('loadEventFired', handler);

        setTimeout(resolve, 10000);
      });

      console.log(`✓ Navigated to ${targetUrl} with restored session`);

      await browserService.closeSession(browserSession.sessionId!);
      return c.json({
        success: true,
        message: 'Session demonstration completed successfully',
        details: {
          cookiesLoaded: storedSession.cookies.length,
          localStorageItems: Object.keys(storedSession.localStorageData).length,
          sessionStorageItems: Object.keys(storedSession.sessionStorageData).length,
          targetUrl: targetUrl,
        },
      });
    } catch (error) {
      console.error('Session demonstration failed:', error);
      return c.json({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      });
    }
  });

app.route('/v1/simulated', SimulatedLoginHandler);

export default app;

export { ConnectionsWorkflow } from '../workflow/connections-workflow';
export { Connections } from '../agent/connection-agent';
export { CoordinatorDO } from '../coordinator/coordinator-do';
