import { env, runDurableObjectAlarm } from 'cloudflare:test';
import { describe, it, expect, vi } from 'vitest';
import { AgentState } from '../../src/agent/types';

describe('Connections DO Integration Tests', () => {
  it('should destroy the DO', async () => {
    // Arrange
    const connectionsId = env.Connections.idFromName(`referenceId321`);
    const stub = env.Connections.get(connectionsId);
    await stub.setup('facebook', 'u_userId', 'referenceId321');

    const initialState: AgentState = await stub.state;

    // Assert initial state
    expect(initialState).toBeDefined();
    expect(initialState.platformId).toBe('facebook');
    expect(initialState.userId).toBe('u_userId');
    expect(initialState.referenceId).toBe('referenceId321');

    // Act
    await expect(stub.deleteAll()).resolves.not.toThrow();
  });

  // it should schedule a cleanup event once setup is called
  it('should schedule a cleanup event', async () => {
    // Arrange
    const connectionsId = env.Connections.idFromName(`referenceId2`);
    const stub = env.Connections.get(connectionsId);
    await stub.setup('facebook', 'u_userId', 'referenceId2');

    // Act
    const schedules = (await stub.getSchedules()) as any[];
    expect(schedules).toBeDefined();
    expect(schedules.length).toBe(1);
    expect(schedules[0].callback).toBe('deleteAll');
    expect(schedules[0].time).toBeGreaterThan(Date.now() / 1000);

    // Simulate time passing to expiration time
    vi.setSystemTime(schedules[0].time * 1000 + 1000);

    // Trigger scheduled callback
    const success = await runDurableObjectAlarm(stub);
    expect(success).toBe(true);
    const finalState: AgentState = await stub.state;
    expect(finalState).toBeUndefined();
  });
});
