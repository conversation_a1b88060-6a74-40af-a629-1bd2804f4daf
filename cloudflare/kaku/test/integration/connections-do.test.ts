import { env } from 'cloudflare:test';
import { describe, it, expect } from 'vitest';
import { AgentState } from '../../src/agent/types';

describe('Connections DO Integration Tests', () => {
  it('should destroy the DO', async () => {
    // Arrange
    const connectionsId = env.Connections.idFromName(`test1`);
    const stub = env.Connections.get(connectionsId);
    await stub.setup('facebook', 'u_userId', 'referenceId');

    const initialState: AgentState = await stub.state;

    // Assert initial state
    expect(initialState).toBeDefined();
    expect(initialState.platformId).toBe('facebook');
    expect(initialState.userId).toBe('u_userId');
    expect(initialState.referenceId).toBe('referenceId');

    // Act
    await expect(stub.deleteAll()).resolves.not.toThrow();
  });
});
