# Durable Objects Test Script

This script allows you to list all durable objects in a Cloudflare namespace using the Cloudflare API.

## Prerequisites

1. **API Token**: Make sure you have a valid Cloudflare API token in your `.dev.vars` file:
   ```
   CLOUDFLARE_API_TOKEN=your_token_here
   ```

2. **Account ID**: The script uses the account ID `dc1aae994a7608ab1e59a843ff0e5a31` (from `wrangler whoami`)

3. **Namespace ID**: Currently configured to use `eb7327151cdf42af8ec63cc7e93ae5eb`

## Usage

### Run the script directly:
```bash
npm run test:list-durable-objects
```

### Or run with tsx directly:
```bash
node --import tsx test/scripts/listDurableObjects.ts
```

## Output

The script will display:

1. **Configuration Info**: Account ID, Namespace ID
2. **Progress**: Real-time fetching progress with pagination
3. **Results Table**: All durable objects with their IDs and storage status
4. **Summary Statistics**: 
   - Total objects found
   - Objects with stored data
   - Objects without stored data
   - Fetch duration

## Example Output

```
🚀 Starting Durable Objects Test Script
Account ID: dc1aae994a7608ab1e59a843ff0e5a31
Namespace ID: eb7327151cdf42af8ec63cc7e93ae5eb

📡 Fetching durable objects...
Fetched 685 objects (Total so far: 685)
✅ Fetch completed in 2152ms

📦 Durable Objects Found:
================================================================================
| ID                                    | Has Stored Data |
--------------------------------------------------------------------------------
| 001fba60ae51eba6b21b4fa6e8c402fe986f943674c440a8a5349d0821831923 | ✅ Yes           |
| 005210b05e7006fe52a4d52a2f2dae71c663a4338d5768b6041777ea5e1c37ad | ✅ Yes           |
...

📊 Summary:
  • Objects with stored data: 673
  • Objects without stored data: 12
  • Total objects: 685
```

## Features

- **Automatic Pagination**: Handles large numbers of durable objects automatically
- **Progress Tracking**: Shows real-time progress during fetching
- **Error Handling**: Provides helpful error messages for common issues
- **Environment Loading**: Automatically loads API token from `.dev.vars`
- **Formatted Output**: Clean, readable table format with emojis

## Configuration

To use a different namespace ID, modify the `NAMESPACE_ID` constant in the script:

```typescript
const NAMESPACE_ID = 'your_namespace_id_here';
```

## API Permissions Required

Your Cloudflare API token needs the following permissions:
- **Account**: Read access
- **Workers Scripts**: Read access (for durable objects)

## Troubleshooting

- **401 Error**: Check if your API token is valid and has correct permissions
- **404 Error**: Verify the namespace ID is correct
- **403 Error**: Ensure your API token has permission to access durable objects
- **Environment Error**: Make sure `CLOUDFLARE_API_TOKEN` is set in `.dev.vars`
