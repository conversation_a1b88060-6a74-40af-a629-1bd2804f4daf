import * as fs from 'fs';
import * as path from 'path';

// Load environment variables from .dev.vars manually
function loadEnvVars() {
  try {
    const envPath = path.join(process.cwd(), '.dev.vars');
    const envContent = fs.readFileSync(envPath, 'utf8');

    envContent.split('\n').forEach((line) => {
      const trimmedLine = line.trim();
      if (trimmedLine && !trimmedLine.startsWith('#')) {
        const [key, ...valueParts] = trimmedLine.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=').replace(/^['"]|['"]$/g, ''); // Remove quotes
          process.env[key] = value;
        }
      }
    });
  } catch (error) {
    console.warn('Could not load .dev.vars file:', error);
  }
}

// Load environment variables
loadEnvVars();

interface DurableObject {
  id: string;
  hasStoredData: boolean;
}

interface CloudflareAPIResponse {
  success: boolean;
  errors: Array<{
    code: number;
    message: string;
    documentation_url?: string;
    source?: {
      pointer: string;
    };
  }>;
  messages: Array<{
    code: number;
    message: string;
    documentation_url?: string;
    source?: {
      pointer: string;
    };
  }>;
  result: DurableObject[];
  result_info?: {
    count: number;
    cursor?: string;
    page: number;
    per_page: number;
    total_count: number;
  };
}

interface ListDurableObjectsOptions {
  cursor?: string;
  limit?: number;
}

class CloudflareDurableObjectsClient {
  private readonly accountId: string;
  private readonly apiToken: string;
  private readonly baseUrl = 'https://api.cloudflare.com/client/v4';

  constructor(accountId: string, apiToken: string) {
    this.accountId = accountId;
    this.apiToken = apiToken;
  }

  /**
   * List all durable objects in a given namespace
   * @param namespaceId - The ID of the durable object namespace
   * @param options - Optional parameters for pagination
   */
  async listDurableObjects(
    namespaceId: string,
    options: ListDurableObjectsOptions = {},
  ): Promise<CloudflareAPIResponse> {
    const url = new URL(
      `${this.baseUrl}/accounts/${this.accountId}/workers/durable_objects/namespaces/${namespaceId}/objects`,
    );

    // Add query parameters
    if (options.cursor) {
      url.searchParams.append('cursor', options.cursor);
    }
    if (options.limit) {
      url.searchParams.append('limit', options.limit.toString());
    }

    const response = await fetch(url.toString(), {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${this.apiToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}`);
    }

    return (await response.json()) as CloudflareAPIResponse;
  }

  /**
   * List all durable objects with automatic pagination
   * @param namespaceId - The ID of the durable object namespace
   * @param limit - Number of objects per page (default: 1000, max: 10000)
   */
  async listAllDurableObjects(namespaceId: string, limit: number = 1000): Promise<DurableObject[]> {
    const allObjects: DurableObject[] = [];
    let cursor: string | undefined;

    do {
      const response = await this.listDurableObjects(namespaceId, { cursor, limit });

      if (!response.success) {
        console.error('API Error:', response.errors);
        throw new Error(`API call failed: ${response.errors.map((e) => e.message).join(', ')}`);
      }

      allObjects.push(...response.result);
      cursor = response.result_info?.cursor;

      console.log(`Fetched ${response.result.length} objects (Total so far: ${allObjects.length})`);

      if (response.result_info?.total_count) {
        console.log(`Progress: ${allObjects.length}/${response.result_info.total_count}`);
      }
    } while (cursor);

    return allObjects;
  }
}

/**
 * Display durable objects in a formatted table
 */
function displayDurableObjects(objects: DurableObject[]): void {
  if (objects.length === 0) {
    console.log('No durable objects found in this namespace.');
    return;
  }

  console.log('\n📦 Durable Objects Found:');
  console.log('='.repeat(80));
  console.log('| ID'.padEnd(40) + '| Has Stored Data |');
  console.log('-'.repeat(80));

  objects.forEach((obj) => {
    const id = obj.id.padEnd(38);
    const hasData = obj.hasStoredData ? '✅ Yes' : '❌ No';
    console.log(`| ${id} | ${hasData.padEnd(15)} |`);
  });

  console.log('-'.repeat(80));
  console.log(`Total: ${objects.length} durable objects`);

  // Summary statistics
  const withData = objects.filter((obj) => obj.hasStoredData).length;
  const withoutData = objects.length - withData;

  console.log('\n📊 Summary:');
  console.log(`  • Objects with stored data: ${withData}`);
  console.log(`  • Objects without stored data: ${withoutData}`);
  console.log(`  • Total objects: ${objects.length}`);
}

/**
 * Main function to test listing durable objects
 */
async function main() {
  // Configuration
  const ACCOUNT_ID = 'dc1aae994a7608ab1e59a843ff0e5a31'; // From wrangler whoami
  const NAMESPACE_ID = 'eb7327151cdf42af8ec63cc7e93ae5eb'; // Provided namespace ID
  const API_TOKEN = process.env.CLOUDFLARE_API_TOKEN;

  if (!API_TOKEN) {
    console.error('❌ Error: CLOUDFLARE_API_TOKEN not found in environment variables.');
    console.log('Make sure your .dev.vars file contains the CLOUDFLARE_API_TOKEN.');
    process.exit(1);
  }

  console.log('🚀 Starting Durable Objects Test Script');
  console.log(`Account ID: ${ACCOUNT_ID}`);
  console.log(`Namespace ID: ${NAMESPACE_ID}`);
  console.log('');

  try {
    const client = new CloudflareDurableObjectsClient(ACCOUNT_ID, API_TOKEN);

    console.log('📡 Fetching durable objects...');
    const startTime = Date.now();

    const objects = await client.listAllDurableObjects(NAMESPACE_ID);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`✅ Fetch completed in ${duration}ms`);

    displayDurableObjects(objects);
  } catch (error) {
    console.error('❌ Error occurred:', error);

    if (error instanceof Error) {
      console.error('Error message:', error.message);

      // Check for common issues
      if (error.message.includes('401')) {
        console.log('\n💡 Tip: Check if your API token is valid and has the correct permissions.');
      } else if (error.message.includes('404')) {
        console.log('\n💡 Tip: Check if the namespace ID is correct.');
      } else if (error.message.includes('403')) {
        console.log('\n💡 Tip: Check if your API token has permission to access durable objects.');
      }
    }

    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { CloudflareDurableObjectsClient, main };
